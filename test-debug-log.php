<?php
/**
 * Test debug logging
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('../../../wp-load.php');

echo "<h2>Debug Log Test</h2>";

// Test error logging
error_log("TEST: Debug logging is working - " . date('Y-m-d H:i:s'));

echo "<p>Test log entry written. Check debug.log file.</p>";

// Check if WP_DEBUG is enabled
echo "<p><strong>WP_DEBUG:</strong> " . (defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled') . "</p>";
echo "<p><strong>WP_DEBUG_LOG:</strong> " . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'Enabled' : 'Disabled') . "</p>";

// Check debug log file location
$debug_log_path = WP_CONTENT_DIR . '/debug.log';
echo "<p><strong>Debug Log Path:</strong> {$debug_log_path}</p>";
echo "<p><strong>Debug Log Exists:</strong> " . (file_exists($debug_log_path) ? 'Yes' : 'No') . "</p>";

if (file_exists($debug_log_path)) {
    $log_size = filesize($debug_log_path);
    echo "<p><strong>Debug Log Size:</strong> " . number_format($log_size) . " bytes</p>";
    
    if ($log_size > 0) {
        $last_lines = file($debug_log_path);
        $last_lines = array_slice($last_lines, -5); // Get last 5 lines
        echo "<h3>Last 5 lines of debug.log:</h3>";
        echo "<pre>" . htmlspecialchars(implode('', $last_lines)) . "</pre>";
    }
}
?>
