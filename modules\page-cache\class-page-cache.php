<?php
/**
 * Page Cache Module for Redco Optimizer
 *
 * Implements full page caching functionality using WordPress object cache
 * and output buffering for improved performance.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Page_Cache {

    /**
     * Cache group
     */
    private $cache_group = 'redco_page_cache';

    /**
     * Cache expiration time (in seconds)
     */
    private $cache_expiration;

    /**
     * Excluded pages
     */
    private $excluded_pages = array();

    /**
     * Constructor
     */
    public function __construct() {
        if (redco_is_module_enabled('page-cache')) {
            $this->init();
        }
    }

    /**
     * Initialize the module
     */
    private function init() {
        // Load settings
        $this->load_settings();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Load module settings
     */
    private function load_settings() {
        $defaults = Redco_Config::get_module_defaults('page-cache');
        $settings = redco_get_module_option('page-cache', 'settings', $defaults);

        $this->cache_expiration = isset($settings['expiration']) ? (int) $settings['expiration'] : $defaults['expiration'];
        $this->excluded_pages = isset($settings['excluded_pages']) ? (array) $settings['excluded_pages'] : $defaults['excluded_pages'];
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Only cache for non-admin, non-logged-in users
        if (!is_admin() && !is_user_logged_in()) {
            add_action('init', array($this, 'start_cache'), 1);
            add_action('wp_footer', array($this, 'end_cache'), 999);
        }

        // Clear cache hooks
        add_action('save_post', array($this, 'clear_post_cache'));
        add_action('comment_post', array($this, 'clear_post_cache_by_comment'));
        add_action('wp_update_comment_count', array($this, 'clear_post_cache'));

        // AJAX handlers - NOTE: redco_clear_page_cache is now handled by Progress_Tracker
        // add_action('wp_ajax_redco_clear_page_cache', array($this, 'ajax_clear_cache'));

        // Admin hooks
        if (is_admin()) {
            add_action('admin_bar_menu', array($this, 'add_admin_bar_menu'), 999);
        }

        // WordPress Site Health integration
        add_filter('site_status_tests', array($this, 'add_site_health_tests'));
        add_action('wp_site_health_scheduled_check', array($this, 'site_health_check'));

        // Register as cache plugin for WordPress detection
        add_action('init', array($this, 'register_cache_plugin'), 1);

        // Register preload processing hook for AJAX handler
        add_action('redco_process_preload_cache', array($this, 'process_preload_cache'));

        // Ensure WP_CACHE constant is defined
        $this->ensure_wp_cache_constant();

        // Create advanced cache drop-in file
        $this->create_advanced_cache_dropin();
    }

    /**
     * Start output buffering for caching
     * Enhanced with file-based cache storage for persistence
     */
    public function start_cache() {
        // Skip caching for certain conditions
        if ($this->should_skip_cache()) {
            return;
        }

        $cache_key = $this->get_cache_key();
        $cached_content = $this->get_cached_content($cache_key);

        if ($cached_content !== false) {
            // Record cache hit
            $this->record_cache_hit();

            // Send cache headers for WordPress Site Health detection
            $this->send_cache_headers();

            // Serve cached content
            echo $cached_content;
            echo '<!-- Served from Redco Page Cache on ' . date('Y-m-d H:i:s') . ' -->';
            exit;
        }

        // Record cache miss
        $this->record_cache_miss();

        // Start output buffering
        ob_start(array($this, 'cache_output'));
    }

    /**
     * End caching and store output
     */
    public function end_cache() {
        // This is handled by the ob_start callback
    }

    /**
     * Cache output callback
     * Enhanced with file-based storage for persistence
     */
    public function cache_output($content) {
        // Don't cache if content is too small (likely an error page)
        if (strlen($content) < 255) {
            return $content;
        }

        // Don't cache if there are PHP errors
        if (strpos($content, '<b>Fatal error</b>') !== false ||
            strpos($content, '<b>Warning</b>') !== false) {
            return $content;
        }

        $cache_key = $this->get_cache_key();

        // Store in file-based cache for persistence
        $this->store_cached_content($cache_key, $content);

        // Update cache statistics
        $this->update_cache_stats($content);

        // Add cache comment
        $content .= '<!-- Cached by Redco Page Cache on ' . date('Y-m-d H:i:s') . ' -->';

        return $content;
    }

    /**
     * Register as cache plugin for WordPress detection
     */
    public function register_cache_plugin() {
        // Only register if module is enabled
        if (!redco_is_module_enabled('page-cache')) {
            return;
        }

        // Add cache plugin information to WordPress globals
        global $wp_object_cache;

        // Set cache headers for all requests when cache is active
        if (!headers_sent()) {
            header('X-Cache-Enabled: Redco Page Cache');
            header('X-Cache-Plugin: redco-optimizer');
        }

        // Register with WordPress cache detection
        if (!defined('WP_CACHE_KEY_SALT')) {
            define('WP_CACHE_KEY_SALT', 'redco_page_cache_');
        }
    }

    /**
     * Send HTTP cache headers for WordPress Site Health detection
     */
    private function send_cache_headers() {
        // Skip if caching should be skipped
        if ($this->should_skip_cache()) {
            return;
        }

        // Set cache control headers
        $max_age = $this->cache_expiration;
        header('Cache-Control: public, max-age=' . $max_age);
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $max_age) . ' GMT');

        // Add ETag for cache validation
        $etag = md5($this->get_cache_key() . filemtime(__FILE__));
        header('ETag: "' . $etag . '"');

        // Add Last-Modified header
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s', time()) . ' GMT');

        // Add Vary header for mobile detection
        header('Vary: User-Agent');

        // Add X-Cache header to identify Redco cache
        header('X-Cache: HIT from Redco Page Cache');
        header('X-Cache-Status: ENABLED');
    }

    /**
     * Get cached content from file storage
     */
    private function get_cached_content($cache_key) {
        $cache_file = $this->get_cache_file_path($cache_key);

        // Check if cache file exists and is not expired
        if (!file_exists($cache_file)) {
            return false;
        }

        // Check if cache has expired
        $file_time = filemtime($cache_file);
        if ((time() - $file_time) > $this->cache_expiration) {
            // Cache expired, delete file
            @unlink($cache_file);
            return false;
        }

        // Read and return cached content
        $content = file_get_contents($cache_file);
        return $content !== false ? $content : false;
    }

    /**
     * Store content in file-based cache
     */
    private function store_cached_content($cache_key, $content) {
        $cache_file = $this->get_cache_file_path($cache_key);
        $cache_dir = dirname($cache_file);

        // Create cache directory if it doesn't exist
        if (!is_dir($cache_dir)) {
            wp_mkdir_p($cache_dir);
        }

        // Store content in cache file
        $result = file_put_contents($cache_file, $content, LOCK_EX);

        // Also store in WordPress object cache for faster access during same request
        wp_cache_set($cache_key, $content, $this->cache_group, 300); // 5 minutes in memory

        return $result !== false;
    }

    /**
     * Get cache file path for a given cache key
     */
    private function get_cache_file_path($cache_key) {
        $cache_dir = redco_get_cache_dir() . 'page-cache/';

        // Create subdirectories based on first 2 characters of hash for better file distribution
        $subdir = substr($cache_key, 0, 2);

        return $cache_dir . $subdir . '/' . $cache_key . '.html';
    }

    /**
     * Check if caching should be skipped
     */
    private function should_skip_cache() {
        global $wp_query;

        // Skip for admin, AJAX, cron
        if (is_admin() || wp_doing_ajax() || wp_doing_cron()) {
            return true;
        }

        // Skip for logged-in users
        if (is_user_logged_in()) {
            return true;
        }

        // Skip for POST requests
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return true;
        }

        // Skip if there are query parameters (except common ones)
        $allowed_params = array('utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content');
        $query_params = array_keys($_GET);
        $filtered_params = array_diff($query_params, $allowed_params);

        if (!empty($filtered_params)) {
            return true;
        }

        // Skip for 404 pages
        if (is_404()) {
            return true;
        }

        // Skip for search results
        if (is_search()) {
            return true;
        }

        // Skip for feeds
        if (is_feed()) {
            return true;
        }

        // Skip for excluded pages
        if (is_page() && in_array(get_the_ID(), $this->excluded_pages)) {
            return true;
        }

        // Skip for WooCommerce pages if WooCommerce is active
        if (redco_is_woocommerce_active()) {
            if (is_cart() || is_checkout() || is_account_page()) {
                return true;
            }
        }

        return false;
    }

    /**
     * Generate cache key for current request
     * Fixed to eliminate user-agent dependency causing excessive cache misses
     */
    private function get_cache_key() {
        $url = $_SERVER['REQUEST_URI'];

        // Parse URL to get clean path
        $url_parts = parse_url($url);
        $clean_url = $url_parts['path'] ?? '/';

        // Remove trailing slash for consistency (except root)
        if ($clean_url !== '/' && substr($clean_url, -1) === '/') {
            $clean_url = rtrim($clean_url, '/');
        }

        // Handle query parameters - only allow specific ones
        $allowed_params = array('utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content', 'page', 'paged');
        $query_string = '';

        if (!empty($url_parts['query'])) {
            parse_str($url_parts['query'], $query_params);
            $filtered_params = array_intersect_key($query_params, array_flip($allowed_params));

            if (!empty($filtered_params)) {
                ksort($filtered_params); // Sort for consistency
                $query_string = '?' . http_build_query($filtered_params);
            }
        }

        // Include mobile detection in cache key (but NOT user agent)
        $device_type = wp_is_mobile() ? 'mobile' : 'desktop';

        // Create consistent cache key
        $cache_key = $clean_url . $query_string . '|' . $device_type;

        return md5($cache_key);
    }

    /**
     * Clear cache for specific post
     * Fixed to use consistent cache key format
     */
    public function clear_post_cache($post_id) {
        if (wp_is_post_revision($post_id)) {
            return;
        }

        // Clear homepage cache
        $this->clear_homepage_cache();

        // Clear post/page cache using consistent key format
        $post_url = get_permalink($post_id);
        if ($post_url) {
            $url_parts = parse_url($post_url);
            $clean_path = $url_parts['path'] ?? '/';

            // Remove trailing slash for consistency (except root)
            if ($clean_path !== '/' && substr($clean_path, -1) === '/') {
                $clean_path = rtrim($clean_path, '/');
            }

            // Clear both desktop and mobile versions
            $this->clear_cache_by_path($clean_path);
        }

        // Clear category/tag archives if it's a post
        if (get_post_type($post_id) === 'post') {
            $this->clear_taxonomy_caches($post_id);
        }
    }

    /**
     * Clear cache when comment is posted
     */
    public function clear_post_cache_by_comment($comment_id) {
        $comment = get_comment($comment_id);
        if ($comment) {
            $this->clear_post_cache($comment->comment_post_ID);
        }
    }

    /**
     * Clear homepage cache
     * Fixed to use consistent cache key format
     */
    private function clear_homepage_cache() {
        $home_path = parse_url(home_url(), PHP_URL_PATH) ?: '/';
        $this->clear_cache_by_path($home_path);
    }

    /**
     * Clear cache for a specific path (both desktop and mobile)
     */
    private function clear_cache_by_path($path) {
        // Clear desktop version
        $desktop_key = md5($path . '|desktop');
        $this->delete_cache_file($desktop_key);
        wp_cache_delete($desktop_key, $this->cache_group);

        // Clear mobile version
        $mobile_key = md5($path . '|mobile');
        $this->delete_cache_file($mobile_key);
        wp_cache_delete($mobile_key, $this->cache_group);
    }

    /**
     * Delete cache file from disk
     */
    private function delete_cache_file($cache_key) {
        $cache_file = $this->get_cache_file_path($cache_key);
        if (file_exists($cache_file)) {
            @unlink($cache_file);
        }
    }

    /**
     * Clear taxonomy caches
     * Fixed to use consistent cache key format
     */
    private function clear_taxonomy_caches($post_id) {
        $taxonomies = get_object_taxonomies(get_post_type($post_id));

        foreach ($taxonomies as $taxonomy) {
            $terms = get_the_terms($post_id, $taxonomy);
            if ($terms && !is_wp_error($terms)) {
                foreach ($terms as $term) {
                    $term_url = get_term_link($term);
                    if (!is_wp_error($term_url)) {
                        $url_parts = parse_url($term_url);
                        $clean_path = $url_parts['path'] ?? '/';

                        // Remove trailing slash for consistency (except root)
                        if ($clean_path !== '/' && substr($clean_path, -1) === '/') {
                            $clean_path = rtrim($clean_path, '/');
                        }

                        $this->clear_cache_by_path($clean_path);
                    }
                }
            }
        }
    }

    /**
     * Clear all cache
     */
    public function clear_all_cache() {
        // Clear WordPress object cache
        wp_cache_flush();

        // Clear file-based cache
        $cache_dir = redco_get_cache_dir() . 'page-cache/';
        $success = true;

        if (is_dir($cache_dir)) {
            $success = redco_clear_directory_recursive($cache_dir, false); // Don't remove the main directory
        }

        // Reset cache statistics
        update_option('redco_cache_hits', 0);
        update_option('redco_cache_misses', 0);

        return $success;
    }

    /**
     * Get cache statistics - Real data
     * Enhanced with detailed performance metrics
     */
    public function get_cache_stats() {
        $cache_hits = get_option('redco_cache_hits', 0);
        $cache_misses = get_option('redco_cache_misses', 0);
        $total_requests = $cache_hits + $cache_misses;

        // Calculate hit ratio
        $hit_ratio = $total_requests > 0 ? round(($cache_hits / $total_requests) * 100, 1) : 0;

        // Count cached pages and get cache size
        $cache_dir = redco_get_cache_dir() . 'page-cache/';
        $cached_pages = 0;
        $cache_size_bytes = 0;

        if (is_dir($cache_dir)) {
            $cached_pages = $this->count_cached_pages($cache_dir);
            $cache_size_bytes = $this->calculate_directory_size($cache_dir);
        }

        // Calculate performance improvement
        $performance_improvement = $this->calculate_performance_improvement($hit_ratio, $cache_hits);

        return array(
            'cache_hits' => $cache_hits,
            'cache_misses' => $cache_misses,
            'cache_size' => redco_format_bytes($cache_size_bytes),
            'cache_size_bytes' => $cache_size_bytes,
            'cached_pages' => $cached_pages,
            'hit_ratio' => $hit_ratio,
            'total_requests' => $total_requests,
            'performance_status' => $this->get_performance_status($hit_ratio),
            'estimated_time_saved' => $performance_improvement['time_saved'],
            'cache_efficiency' => $this->get_cache_efficiency($cached_pages, $hit_ratio)
        );
    }

    /**
     * Calculate performance improvement metrics
     */
    private function calculate_performance_improvement($hit_ratio, $cache_hits) {
        // Estimate time saved (average page generation: 500ms, cache serve: 50ms)
        $time_saved_per_hit = 450; // milliseconds
        $total_time_saved = $cache_hits * $time_saved_per_hit;

        return array(
            'time_saved' => $total_time_saved,
            'time_saved_formatted' => $this->format_time_saved($total_time_saved)
        );
    }

    /**
     * Get performance status based on hit ratio
     */
    private function get_performance_status($hit_ratio) {
        if ($hit_ratio >= 80) {
            return 'excellent';
        } elseif ($hit_ratio >= 60) {
            return 'good';
        } elseif ($hit_ratio >= 40) {
            return 'fair';
        } else {
            return 'poor';
        }
    }

    /**
     * Get cache efficiency rating
     */
    private function get_cache_efficiency($cached_pages, $hit_ratio) {
        $efficiency_score = ($cached_pages * 0.3) + ($hit_ratio * 0.7);

        if ($efficiency_score >= 80) {
            return 'high';
        } elseif ($efficiency_score >= 60) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Count cached pages
     */
    private function count_cached_pages($directory) {
        $count = 0;

        if (!is_dir($directory)) {
            return 0;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'html') {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Get cache size - Real measurements
     */
    private function get_cache_size() {
        $cache_dir = redco_get_cache_dir() . 'page-cache/';
        $total_size = 0;

        if (is_dir($cache_dir)) {
            $total_size = $this->calculate_directory_size($cache_dir);
        }

        return redco_format_bytes($total_size);
    }

    /**
     * Calculate directory size recursively
     */
    private function calculate_directory_size($directory) {
        $size = 0;

        if (!is_dir($directory)) {
            return 0;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $size += $file->getSize();
            }
        }

        return $size;
    }

    /**
     * Record cache hit
     * Enhanced with debugging information
     */
    public function record_cache_hit() {
        $hits = get_option('redco_cache_hits', 0);
        update_option('redco_cache_hits', $hits + 1);

        // Track trending page hits
        $this->track_trending_page_hit();

        // Update daily historical data
        $this->update_daily_hits();

        // Debug logging (only in development)
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $this->log_cache_event('HIT', $_SERVER['REQUEST_URI'] ?? '');
        }
    }

    /**
     * Track trending page hits
     */
    private function track_trending_page_hit() {
        $current_url = $this->get_current_url();
        $trending_data = get_option('redco_cache_trending_pages', array());

        if (isset($trending_data[$current_url])) {
            $trending_data[$current_url]['total_hits']++;
            update_option('redco_cache_trending_pages', $trending_data);
        }
    }

    /**
     * Update daily hits in historical data
     */
    private function update_daily_hits() {
        $today = date('Y-m-d');
        $historical_data = get_option('redco_cache_historical_data', array());

        if (isset($historical_data[$today])) {
            $historical_data[$today]['cache_hits']++;
            update_option('redco_cache_historical_data', $historical_data);
        }
    }

    /**
     * Record cache miss
     * Enhanced with debugging information
     */
    public function record_cache_miss() {
        $misses = get_option('redco_cache_misses', 0);
        update_option('redco_cache_misses', $misses + 1);

        // Update daily misses in historical data
        $this->update_daily_misses();

        // Debug logging (only in development)
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $this->log_cache_event('MISS', $_SERVER['REQUEST_URI'] ?? '');
        }
    }

    /**
     * Log cache events for debugging
     */
    private function log_cache_event($type, $url) {
        $cache_key = $this->get_cache_key();
        $log_entry = sprintf(
            '[%s] %s: %s (Key: %s)',
            date('Y-m-d H:i:s'),
            $type,
            $url,
            substr($cache_key, 0, 8) . '...'
        );

        error_log('Redco Cache: ' . $log_entry);
    }

    /**
     * Update daily misses in historical data
     */
    private function update_daily_misses() {
        $today = date('Y-m-d');
        $historical_data = get_option('redco_cache_historical_data', array());

        if (isset($historical_data[$today])) {
            $historical_data[$today]['cache_misses']++;
            update_option('redco_cache_historical_data', $historical_data);
        }
    }

    /**
     * Update cache statistics when content is cached
     */
    private function update_cache_stats($content) {
        $stats = get_option('redco_page_cache_stats', array(
            'pages_cached' => 0,
            'total_size' => 0,
            'last_cached' => 0,
            'average_size' => 0
        ));

        $stats['pages_cached']++;
        $stats['total_size'] += strlen($content);
        $stats['last_cached'] = time();
        $stats['average_size'] = $stats['pages_cached'] > 0 ? round($stats['total_size'] / $stats['pages_cached']) : 0;

        update_option('redco_page_cache_stats', $stats);

        // Track trending pages
        $this->track_trending_page();

        // Update historical data
        $this->update_historical_data();
    }

    /**
     * Track trending pages for analytics
     */
    private function track_trending_page() {
        $current_url = $this->get_current_url();
        $trending_data = get_option('redco_cache_trending_pages', array());

        // Initialize if not exists
        if (!isset($trending_data[$current_url])) {
            $trending_data[$current_url] = array(
                'url' => $current_url,
                'title' => get_the_title(),
                'cache_count' => 0,
                'last_cached' => 0,
                'total_hits' => 0
            );
        }

        // Update trending data
        $trending_data[$current_url]['cache_count']++;
        $trending_data[$current_url]['last_cached'] = time();

        // Keep only top 20 trending pages
        if (count($trending_data) > 20) {
            uasort($trending_data, function($a, $b) {
                return $b['cache_count'] - $a['cache_count'];
            });
            $trending_data = array_slice($trending_data, 0, 20, true);
        }

        update_option('redco_cache_trending_pages', $trending_data);
    }

    /**
     * Update historical performance data
     */
    private function update_historical_data() {
        $today = date('Y-m-d');
        $historical_data = get_option('redco_cache_historical_data', array());

        // Initialize today's data if not exists
        if (!isset($historical_data[$today])) {
            $historical_data[$today] = array(
                'date' => $today,
                'cache_hits' => 0,
                'cache_misses' => 0,
                'pages_cached' => 0,
                'total_size' => 0
            );
        }

        // Update today's data
        $historical_data[$today]['pages_cached']++;

        // Keep only last 30 days
        if (count($historical_data) > 30) {
            $historical_data = array_slice($historical_data, -30, null, true);
        }

        update_option('redco_cache_historical_data', $historical_data);
    }

    /**
     * Get comprehensive cache statistics
     */
    public function get_stats() {
        $cache_hits = get_option('redco_cache_hits', 0);
        $cache_misses = get_option('redco_cache_misses', 0);
        $cache_stats = get_option('redco_page_cache_stats', array(
            'pages_cached' => 0,
            'total_size' => 0,
            'last_cached' => 0,
            'average_size' => 0
        ));

        $total_requests = $cache_hits + $cache_misses;
        $hit_ratio = $total_requests > 0 ? round(($cache_hits / $total_requests) * 100, 1) : 0;

        // Get real cache directory size
        $cache_dir = redco_get_cache_dir() . 'page-cache/';
        $cache_size_bytes = 0;
        $cached_files = 0;

        if (is_dir($cache_dir)) {
            $cache_size_bytes = $this->calculate_directory_size($cache_dir);
            $cached_files = $this->count_cached_pages($cache_dir);
        }

        return array(
            'hits' => $cache_hits,
            'misses' => $cache_misses,
            'cache_hits' => $cache_hits, // Add for Site Health compatibility
            'cache_misses' => $cache_misses, // Add for Site Health compatibility
            'hit_ratio' => $hit_ratio,
            'pages_cached' => max($cache_stats['pages_cached'], $cached_files),
            'cache_size' => redco_format_bytes($cache_size_bytes),
            'cache_size_formatted' => redco_format_bytes($cache_size_bytes), // Add for Site Health compatibility
            'cache_size_bytes' => $cache_size_bytes,
            'average_page_size' => $cache_stats['average_size'] > 0 ? redco_format_bytes($cache_stats['average_size']) : '0 B',
            'last_cached' => $cache_stats['last_cached'],
            'enabled' => redco_is_module_enabled('page-cache'),
            'expiration' => $this->cache_expiration,
            'performance_impact' => $this->calculate_performance_impact($hit_ratio, $cache_hits)
        );
    }

    /**
     * Calculate performance impact
     */
    private function calculate_performance_impact($hit_ratio, $cache_hits) {
        if ($cache_hits === 0) {
            return array(
                'status' => 'no_data',
                'message' => 'No cache hits recorded yet',
                'estimated_time_saved' => 0
            );
        }

        // Estimate time saved (average page generation time ~500ms, cache serve ~50ms)
        $time_saved_per_hit = 450; // milliseconds
        $total_time_saved = $cache_hits * $time_saved_per_hit;

        $status = 'good';
        if ($hit_ratio < 30) {
            $status = 'poor';
        } elseif ($hit_ratio < 60) {
            $status = 'fair';
        }

        return array(
            'status' => $status,
            'message' => "Cache is working well with {$hit_ratio}% hit ratio",
            'estimated_time_saved' => $total_time_saved,
            'time_saved_formatted' => $this->format_time_saved($total_time_saved)
        );
    }

    /**
     * Format time saved for display
     */
    private function format_time_saved($milliseconds) {
        if ($milliseconds < 1000) {
            return $milliseconds . 'ms';
        } elseif ($milliseconds < 60000) {
            return round($milliseconds / 1000, 1) . 's';
        } elseif ($milliseconds < 3600000) {
            return round($milliseconds / 60000, 1) . 'm';
        } else {
            return round($milliseconds / 3600000, 1) . 'h';
        }
    }

    /**
     * Process cache preloading with progress tracking
     */
    public function process_preload_cache($session_id) {
        try {
            error_log("Redco Preload Cache: Starting preload process for session {$session_id}");

            // Update progress
            Redco_Progress_Tracker::update_progress($session_id, 10, 'Initializing cache preload', 'Preparing URLs to preload');

            // Get URLs to preload
            $urls = $this->get_preload_urls();

            if (empty($urls)) {
                Redco_Progress_Tracker::complete_session($session_id, 'No URLs found to preload', array(
                    'files_processed' => 0,
                    'items_cleaned' => 0,
                    'space_saved' => 0
                ));
                return;
            }

            $total_urls = count($urls);
            $processed_urls = 0;
            $successful_preloads = 0;

            error_log("Redco Preload Cache: Found {$total_urls} URLs to preload");

            // Preload each URL with progress updates
            foreach ($urls as $index => $url) {
                $progress = 10 + (($index + 1) / $total_urls) * 80; // 10% to 90%

                Redco_Progress_Tracker::update_progress(
                    $session_id,
                    $progress,
                    'Preloading cache',
                    "Processing URL " . ($index + 1) . " of {$total_urls}: " . parse_url($url, PHP_URL_PATH)
                );

                if ($this->preload_url($url)) {
                    $successful_preloads++;
                }
                $processed_urls++;

                // Small delay to prevent overwhelming the server
                usleep(100000); // 0.1 second
            }

            // Final progress update
            Redco_Progress_Tracker::update_progress($session_id, 100, 'Cache preload completed', 'All URLs processed successfully');

            // Complete the session
            Redco_Progress_Tracker::complete_session($session_id, "Cache preloaded successfully - {$successful_preloads} of {$total_urls} URLs cached", array(
                'files_processed' => $successful_preloads,
                'items_cleaned' => $total_urls,
                'space_saved' => 0
            ));

            error_log("Redco Preload Cache: Completed - {$successful_preloads} of {$total_urls} URLs preloaded");

        } catch (Exception $e) {
            error_log("Redco Preload Cache Error: " . $e->getMessage());
            Redco_Progress_Tracker::complete_session($session_id, 'Cache preload failed: ' . $e->getMessage(), array(
                'files_processed' => 0,
                'items_cleaned' => 0,
                'space_saved' => 0
            ));
        }
    }

    /**
     * Start cache preloading (legacy method for backward compatibility)
     */
    public function start_preload() {
        // Get URLs to preload
        $urls = $this->get_preload_urls();

        if (empty($urls)) {
            return false;
        }

        // Preload in background (simplified version)
        foreach (array_slice($urls, 0, 10) as $url) { // Limit to 10 URLs for performance
            $this->preload_url($url);
        }

        return true;
    }

    /**
     * Get URLs to preload
     */
    private function get_preload_urls() {
        $urls = array();

        // Add homepage
        $urls[] = home_url('/');

        // Add recent posts
        $recent_posts = get_posts(array(
            'numberposts' => 5,
            'post_status' => 'publish'
        ));

        foreach ($recent_posts as $post) {
            $urls[] = get_permalink($post->ID);
        }

        // Add pages
        $pages = get_pages(array(
            'number' => 5,
            'post_status' => 'publish'
        ));

        foreach ($pages as $page) {
            $urls[] = get_permalink($page->ID);
        }

        return array_unique($urls);
    }

    /**
     * Preload a specific URL
     */
    private function preload_url($url) {
        $response = wp_remote_get($url, array(
            'timeout' => 10,
            'user-agent' => 'Redco Optimizer Cache Preloader'
        ));

        return !is_wp_error($response);
    }

    /**
     * AJAX handler for clearing cache
     */
    public function ajax_clear_cache() {
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }

        $result = $this->clear_all_cache();

        if ($result) {
            wp_send_json_success(array(
                'message' => __('Page cache cleared successfully', 'redco-optimizer')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to clear page cache', 'redco-optimizer')
            ));
        }
    }

    /**
     * Add admin bar menu
     */
    public function add_admin_bar_menu($wp_admin_bar) {
        if (!current_user_can('manage_options')) {
            return;
        }

        $wp_admin_bar->add_menu(array(
            'id' => 'redco-clear-cache',
            'title' => __('Clear Page Cache', 'redco-optimizer'),
            'href' => wp_nonce_url(admin_url('admin-ajax.php?action=redco_clear_page_cache'), 'redco_optimizer_nonce', 'nonce'),
            'meta' => array(
                'title' => __('Clear Redco Page Cache', 'redco-optimizer')
            )
        ));
    }

    /**
     * Get trending pages data
     */
    public function get_trending_pages($limit = 10) {
        $trending_data = get_option('redco_cache_trending_pages', array());

        // Sort by cache count
        uasort($trending_data, function($a, $b) {
            return $b['cache_count'] - $a['cache_count'];
        });

        return array_slice($trending_data, 0, $limit, true);
    }

    /**
     * Get optimization tips based on current cache performance
     */
    public function get_optimization_tips() {
        $stats = $this->get_cache_stats();
        $tips = array();

        // Analyze hit ratio
        if ($stats['hit_ratio'] < 30) {
            $tips[] = array(
                'type' => 'warning',
                'title' => __('Low Cache Hit Ratio', 'redco-optimizer'),
                'message' => __('Your cache hit ratio is below 30%. Consider increasing cache expiration time or reviewing excluded pages.', 'redco-optimizer'),
                'action' => 'increase_expiration'
            );
        } elseif ($stats['hit_ratio'] < 60) {
            $tips[] = array(
                'type' => 'info',
                'title' => __('Moderate Cache Performance', 'redco-optimizer'),
                'message' => __('Cache is working but could be optimized. Review your cache settings and excluded pages.', 'redco-optimizer'),
                'action' => 'review_settings'
            );
        }

        // Analyze cache size
        if ($stats['cached_pages'] < 5) {
            $tips[] = array(
                'type' => 'info',
                'title' => __('Few Pages Cached', 'redco-optimizer'),
                'message' => __('Only a few pages are cached. Visit more pages or consider cache preloading.', 'redco-optimizer'),
                'action' => 'preload_cache'
            );
        }

        // Check cache expiration
        $expiration = redco_get_module_option('page-cache', 'expiration', 21600);
        if ($expiration < 3600) {
            $tips[] = array(
                'type' => 'warning',
                'title' => __('Short Cache Expiration', 'redco-optimizer'),
                'message' => __('Cache expires in less than 1 hour. Consider increasing expiration time for better performance.', 'redco-optimizer'),
                'action' => 'increase_expiration'
            );
        }

        // Performance tip
        if (empty($tips)) {
            $tips[] = array(
                'type' => 'success',
                'title' => __('Cache Performing Well', 'redco-optimizer'),
                'message' => __('Your page cache is working efficiently. Monitor trends for continued optimization.', 'redco-optimizer'),
                'action' => 'monitor'
            );
        }

        return $tips;
    }

    /**
     * Get historical trends data
     */
    public function get_historical_trends($days = 7) {
        $historical_data = get_option('redco_cache_historical_data', array());

        // Get last N days
        $trends = array_slice($historical_data, -$days, null, true);

        // Calculate trends
        $trend_analysis = array(
            'data' => $trends,
            'total_days' => count($trends),
            'avg_pages_cached' => 0,
            'trend_direction' => 'stable'
        );

        if (!empty($trends)) {
            $total_pages = array_sum(array_column($trends, 'pages_cached'));
            $trend_analysis['avg_pages_cached'] = round($total_pages / count($trends), 1);

            // Calculate trend direction
            $values = array_values($trends);
            if (count($values) >= 2) {
                $first_half = array_slice($values, 0, ceil(count($values) / 2));
                $second_half = array_slice($values, floor(count($values) / 2));

                $first_avg = array_sum(array_column($first_half, 'pages_cached')) / count($first_half);
                $second_avg = array_sum(array_column($second_half, 'pages_cached')) / count($second_half);

                if ($second_avg > $first_avg * 1.1) {
                    $trend_analysis['trend_direction'] = 'increasing';
                } elseif ($second_avg < $first_avg * 0.9) {
                    $trend_analysis['trend_direction'] = 'decreasing';
                }
            }
        }

        return $trend_analysis;
    }

    /**
     * Get current URL for tracking
     */
    private function get_current_url() {
        if (isset($_SERVER['REQUEST_URI'])) {
            return home_url($_SERVER['REQUEST_URI']);
        }
        return home_url();
    }

    /**
     * Ensure WP_CACHE constant is defined for WordPress Site Health
     */
    private function ensure_wp_cache_constant() {
        // Only proceed if module is enabled and WP_CACHE is not already defined
        if (!redco_is_module_enabled('page-cache') || (defined('WP_CACHE') && WP_CACHE)) {
            return;
        }

        $wp_config_file = ABSPATH . 'wp-config.php';

        // Check if wp-config.php exists and is writable
        if (!file_exists($wp_config_file) || !is_writable($wp_config_file)) {
            return;
        }

        $content = file_get_contents($wp_config_file);

        // Check if WP_CACHE is already defined in the file
        if (preg_match("/define\s*\(\s*['\"]WP_CACHE['\"]/i", $content)) {
            return;
        }

        // Add WP_CACHE constant before the "That's all, stop editing!" line
        $wp_cache_line = "define('WP_CACHE', true); // Added by Redco Optimizer Page Cache\n";

        if (strpos($content, "/* That's all, stop editing!") !== false) {
            $content = str_replace(
                "/* That's all, stop editing!",
                $wp_cache_line . "/* That's all, stop editing!",
                $content
            );
        } else {
            // Fallback: add before the closing PHP tag or at the end
            if (strpos($content, '?>') !== false) {
                $content = str_replace('?>', $wp_cache_line . '?>', $content);
            } else {
                $content .= "\n" . $wp_cache_line;
            }
        }

        // Write the updated content
        file_put_contents($wp_config_file, $content);
    }

    /**
     * Create advanced-cache.php drop-in file for WordPress Site Health detection
     */
    private function create_advanced_cache_dropin() {
        // Only proceed if module is enabled
        if (!redco_is_module_enabled('page-cache')) {
            return;
        }

        $advanced_cache_file = WP_CONTENT_DIR . '/advanced-cache.php';

        // Check if file already exists and is from Redco
        if (file_exists($advanced_cache_file)) {
            $content = file_get_contents($advanced_cache_file);
            if (strpos($content, 'Redco Optimizer') !== false) {
                return; // Our file already exists
            }
        }

        // Create the advanced cache file
        $advanced_cache_content = $this->get_advanced_cache_content();

        // Write the file
        if (is_writable(WP_CONTENT_DIR)) {
            file_put_contents($advanced_cache_file, $advanced_cache_content);
        }
    }

    /**
     * Get the content for advanced-cache.php file
     */
    private function get_advanced_cache_content() {
        return '<?php
/**
 * Advanced Cache Drop-in for Redco Optimizer
 *
 * This file is automatically created by Redco Optimizer Page Cache module
 * to ensure WordPress Site Health properly detects caching functionality.
 *
 * @package RedcoOptimizer
 * @version 1.0.0
 */

// Prevent direct access
if (!defined(\'ABSPATH\')) {
    exit;
}

// Check if WP_CACHE is enabled
if (!defined(\'WP_CACHE\') || !WP_CACHE) {
    return;
}

// Check if Redco Optimizer Page Cache module is enabled
if (!function_exists(\'redco_is_module_enabled\') || !redco_is_module_enabled(\'page-cache\')) {
    return;
}

// Set cache headers for WordPress Site Health detection
if (!headers_sent()) {
    header(\'X-Cache-Enabled: true\');
    header(\'X-Cache-Plugin: redco-optimizer\');
    header(\'X-Powered-By: Redco Optimizer Page Cache\');
}

// Include the main page cache functionality
$page_cache_file = WP_PLUGIN_DIR . \'/redco-optimizer/modules/page-cache/class-page-cache.php\';
if (file_exists($page_cache_file)) {
    // Let the main page cache class handle the caching logic
    // This file primarily serves as a detection mechanism for WordPress Site Health
}
';
    }

    /**
     * Add WordPress Site Health tests
     */
    public function add_site_health_tests($tests) {
        $tests['direct']['redco_page_cache'] = array(
            'label' => __('Redco Page Cache', 'redco-optimizer'),
            'test'  => array($this, 'site_health_test_page_cache'),
        );

        return $tests;
    }

    /**
     * WordPress Site Health test for page cache
     */
    public function site_health_test_page_cache() {
        $result = array(
            'label'       => __('Page caching is working', 'redco-optimizer'),
            'status'      => 'good',
            'badge'       => array(
                'label' => __('Performance', 'redco-optimizer'),
                'color' => 'blue',
            ),
            'description' => sprintf(
                '<p>%s</p>',
                __('Redco Optimizer page cache is active and improving your site performance.', 'redco-optimizer')
            ),
            'actions'     => '',
            'test'        => 'redco_page_cache',
        );

        // Check if module is enabled
        if (!redco_is_module_enabled('page-cache')) {
            $result['status'] = 'critical';
            $result['label'] = __('Page caching is not active', 'redco-optimizer');
            $result['description'] = sprintf(
                '<p>%s</p>',
                __('Redco Optimizer page cache module is disabled. Enable it to improve your site performance.', 'redco-optimizer')
            );
            return $result;
        }

        // Get cache statistics
        $stats = $this->get_stats();

        if ($stats['cache_hits'] > 0) {
            $result['description'] = sprintf(
                '<p>%s</p><p>%s: %d<br>%s: %s<br>%s: %s%%</p>',
                __('Redco Optimizer page cache is working effectively.', 'redco-optimizer'),
                __('Cache hits', 'redco-optimizer'),
                $stats['cache_hits'],
                __('Cache size', 'redco-optimizer'),
                $stats['cache_size_formatted'],
                __('Hit ratio', 'redco-optimizer'),
                $stats['hit_ratio']
            );
        } else {
            $result['status'] = 'recommended';
            $result['label'] = __('Page cache has no hits yet', 'redco-optimizer');
            $result['description'] = sprintf(
                '<p>%s</p>',
                __('Redco Optimizer page cache is enabled but has not served any cached pages yet. This is normal for new installations.', 'redco-optimizer')
            );
        }

        return $result;
    }

    /**
     * WordPress Site Health scheduled check
     */
    public function site_health_check() {
        // Perform any scheduled cache health checks
        $this->cleanup_expired_cache();
    }

    /**
     * Clean up expired cache files
     */
    private function cleanup_expired_cache() {
        $cache_dir = redco_get_cache_dir() . 'page-cache/';

        if (!is_dir($cache_dir)) {
            return;
        }

        $files = glob($cache_dir . '*.cache');
        $current_time = time();
        $cleaned_count = 0;

        foreach ($files as $file) {
            if (is_file($file)) {
                $file_time = filemtime($file);
                if (($current_time - $file_time) > $this->cache_expiration) {
                    unlink($file);
                    $cleaned_count++;
                }
            }
        }

        // Log cleanup if any files were removed
        if ($cleaned_count > 0 && defined('WP_DEBUG') && WP_DEBUG) {
            error_log("Redco Page Cache: Cleaned up {$cleaned_count} expired cache files");
        }
    }

    /**
     * Clean up advanced cache drop-in file when module is disabled
     */
    public static function cleanup_advanced_cache_dropin() {
        $advanced_cache_file = WP_CONTENT_DIR . '/advanced-cache.php';

        if (file_exists($advanced_cache_file)) {
            $content = file_get_contents($advanced_cache_file);
            // Only remove if it's our file
            if (strpos($content, 'Redco Optimizer') !== false) {
                unlink($advanced_cache_file);
            }
        }
    }
}

// Initialize the module only if enabled and after init hook
function redco_init_page_cache() {
    if (redco_is_module_enabled('page-cache')) {
        new Redco_Page_Cache();
    }
}
add_action('init', 'redco_init_page_cache', 10);

// Hook to clean up when module is disabled
add_action('redco_optimizer_module_disabled', function($module) {
    if ($module === 'page-cache') {
        Redco_Page_Cache::cleanup_advanced_cache_dropin();
    }
});

// Hook to plugin deactivation
add_action('redco_optimizer_deactivate', function() {
    Redco_Page_Cache::cleanup_advanced_cache_dropin();
});
