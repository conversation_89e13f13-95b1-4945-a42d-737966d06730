<?php
/**
 * Test AJAX endpoint availability
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('../../../wp-load.php');

// Check if user is logged in and has admin capabilities
if (!is_user_logged_in() || !current_user_can('manage_options')) {
    echo "<h2>Error: Please log in as an administrator first</h2>";
    echo "<p><a href='" . wp_login_url() . "'>Login</a></p>";
    exit;
}

echo "<h2>AJAX Endpoint Test</h2>";

// Check if the Progress_Tracker class exists and is initialized
if (class_exists('Redco_Progress_Tracker')) {
    echo "<p>✓ Redco_Progress_Tracker class exists</p>";
    
    // Check if the AJAX actions are registered
    global $wp_filter;
    
    $ajax_actions = array(
        'wp_ajax_redco_clear_cache',
        'wp_ajax_redco_get_progress',
        'wp_ajax_redco_clear_page_cache',
        'wp_ajax_redco_clear_minified_cache'
    );
    
    foreach ($ajax_actions as $action) {
        if (isset($wp_filter[$action])) {
            echo "<p>✓ {$action} is registered</p>";
        } else {
            echo "<p>✗ {$action} is NOT registered</p>";
        }
    }
    
} else {
    echo "<p>✗ Redco_Progress_Tracker class does not exist</p>";
}

// Test cache directory
$upload_dir = wp_upload_dir();
$cache_base_dir = $upload_dir['basedir'] . '/redco-optimizer-cache/';

echo "<h3>Cache Directory Status</h3>";
echo "<p><strong>Cache Directory:</strong> {$cache_base_dir}</p>";

if (is_dir($cache_base_dir)) {
    echo "<p>✓ Cache directory exists</p>";
    
    // Count files in each subdirectory
    if ($handle = opendir($cache_base_dir)) {
        while (false !== ($entry = readdir($handle))) {
            if ($entry != "." && $entry != ".." && is_dir($cache_base_dir . $entry)) {
                $file_count = 0;
                $iterator = new RecursiveIteratorIterator(
                    new RecursiveDirectoryIterator($cache_base_dir . $entry, RecursiveDirectoryIterator::SKIP_DOTS),
                    RecursiveIteratorIterator::LEAVES_ONLY
                );
                
                foreach ($iterator as $file) {
                    if ($file->isFile()) {
                        $file_count++;
                    }
                }
                
                echo "<p><strong>{$entry}:</strong> {$file_count} files</p>";
            }
        }
        closedir($handle);
    }
} else {
    echo "<p>✗ Cache directory does not exist</p>";
}

// Generate nonce for testing
$nonce = wp_create_nonce('redco_optimizer_nonce');
echo "<h3>Test AJAX Call</h3>";
echo "<p><strong>Nonce:</strong> {$nonce}</p>";
echo "<button onclick=\"testClearCache()\">Test Clear Cache AJAX</button>";
echo "<div id=\"ajax-result\"></div>";

?>
<script src="<?php echo includes_url('js/jquery/jquery.min.js'); ?>"></script>
<script>
function testClearCache() {
    document.getElementById('ajax-result').innerHTML = 'Testing...';
    
    jQuery.ajax({
        url: '<?php echo admin_url('admin-ajax.php'); ?>',
        type: 'POST',
        data: {
            action: 'redco_clear_cache',
            nonce: '<?php echo $nonce; ?>',
            session_id: 'test_' + Date.now()
        },
        success: function(response) {
            document.getElementById('ajax-result').innerHTML = '<h4>Success:</h4><pre>' + JSON.stringify(response, null, 2) + '</pre>';
        },
        error: function(xhr, status, error) {
            document.getElementById('ajax-result').innerHTML = '<h4>Error:</h4><p>Status: ' + status + '</p><p>Error: ' + error + '</p><p>Response: ' + xhr.responseText + '</p>';
        }
    });
}
</script>
